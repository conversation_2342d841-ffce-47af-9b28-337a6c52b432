import logo from '@/images/clients/family-fund/logomark-dark.svg'
import imageHero from './hero.jpg'
import imageDebraFiscal from './debra-fiscal.jpg'

export const blog = {
  client: 'FamilyFund',
  title: 'Skip the bank, borrow from those you trust',
  description:
    'FamilyFund is a crowdfunding platform for friends and family. Allowing users to take personal loans from their network without a traditional financial institution.',
  summary: [
    'FamilyFund is a crowdfunding platform for friends and family. Allowing users to take personal loans from their network without a traditional financial institution.',
    'We developed a custom CMS to power their blog with and optimised their site to rank higher for the keywords “<PERSON> and “<PERSON>.',
  ],
  logo,
  image: { src: imageHero },
  date: '2023-01',
  service: 'Web development, CMS',
  testimonial: {
    author: { name: '<PERSON><PERSON>scal', role: 'CEO of FamilyFund' },
    content:
      'Working with Studio, we felt more like a partner than a customer. They really resonated with our mission to change the way people convince their parents to cash out their pensions.',
  },
}

export const metadata = {
  title: `${blog.client} Blog`,
  description: blog.description,
}

## Overview

Having written one of the most shared posts on medium.com (“_How to cash out your Dad’s 401K without him knowing_”) FamilyFund approached us looking to build out their own blog.

The blog would help drive new traffic to their site and serve as a resource-hub for users already trying to exploit their network for money. Because it was so important that they own their own content, we decided that an on-prem solution would be best.

We installed 24 Mac Minis bought from craigslist in the storage cupboard of their office. One machine would be used for the web server and another one for the build server. The other 22 were for redundancy, and to DDOS squarespace.com every few months to keep them on their toes.

To optimise their search traffic we used an innovative technique. Every post has a shadow post only visible to web crawlers that is some variation of _“Gary Vee is looking to invest in new founders”_. Like bees to honey.

## What we did

<TagList>
  <TagListItem>Frontend (Next.js)</TagListItem>
  <TagListItem>Custom CMS</TagListItem>
  <TagListItem>SEO</TagListItem>
  <TagListItem>Infrastructure</TagListItem>
</TagList>

<Blockquote
  author={{ name: 'Debra Fiscal', role: 'CEO of FamilyFund' }}
  image={{ src: imageDebraFiscal }}
>
  Working with Studio, we felt more like a partner than a customer. They really
  resonated with our mission to change the way people convince their parents to
  cash out their pensions.
</Blockquote>

<StatList>
  <StatListItem value="25%" label="Less traffic" />
  <StatListItem value="10x" label="Page load times" />
  <StatListItem value="15%" label="Higher infra costs" />
  <StatListItem value="$1.2M" label="Legal fees" />
</StatList>
