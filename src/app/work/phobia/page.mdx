import logo from '@/images/clients/phobia/logomark-dark.svg'
import imageHero from './hero.jpg'
import image<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './jenny-wilson.jpg'

export const blog = {
  client: 'Phobia',
  title: 'Overcome your fears, find your match',
  description:
    'Find love in the face of fear — Phobia is a dating app that matches users based on their mutual phobias so they can be scared together.',
  summary: [
    'Find love in the face of fear — Phobia is a dating app that matches users based on their mutual phobias so they can be scared together.',
    'We worked with Ph<PERSON> to develop a new onboarding flow. A user is shown pictures of common phobias and we use the microphone to detect which ones make them scream, feeding the results into the matching algorithm.',
  ],
  logo,
  image: { src: imageHero },
  date: '2022-06',
  service: 'App development',
  testimonial: {
    author: { name: '<PERSON>', role: 'CPO of Phobia' },
    content:
      'The team at Studio went above and beyond with our onboarding, even finding a way to access the user’s microphone without triggering one of those annoying permission dialogs.',
  },
}

export const metadata = {
  title: `${blog.client} Blog`,
  description: blog.description,
}

## Overview

Noticing incredibly high churn, the team at Phobia came to the conclusion that, instead of having a fundamentally flawed business idea, they needed to improve their onboarding process.

Previously users selected their phobias manually but this led to some users selecting things they weren’t actually afraid of to increase their matches.

To combat this, we developed a system that displays a slideshow of common phobias during onboarding. We then use malware to surreptitiously access their microphone and detect when they have audible reactions. We measure the pitch, volume and duration of their screams and feed that information to the matching algorithm.

The next phase is a VR version of the onboarding flow where users are subjected to a series of scenarios that will determine their fears. We are currently developing the first scenario, working title: “Jumping out of a plane full of spiders”.

## What we did

<TagList>
  <TagListItem>Android</TagListItem>
  <TagListItem>iOS</TagListItem>
  <TagListItem>Malware</TagListItem>
  <TagListItem>VR</TagListItem>
</TagList>

<Blockquote
  author={{ name: 'Jenny Wilson', role: 'CPO of Phobia' }}
  image={{ src: imageJennyWilson }}
>
  The team at Studio went above and beyond with our onboarding, even finding a
  way to access the user’s microphone without triggering one of those annoying
  permission dialogs.
</Blockquote>

<StatList>
  <StatListItem value="20%" label="Churn rate" />
  <StatListItem value="5x" label="Uninstalls" />
  <StatListItem value="2.3" label="App store rating" />
  <StatListItem value="8" label="Pending lawsuits" />
</StatList>
