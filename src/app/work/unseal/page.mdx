import logo from '@/images/clients/unseal/logomark-dark.svg'
import imageHero from './hero.jpg'
import imageE<PERSON><PERSON><PERSON><PERSON><PERSON> from './emily-selman.jpg'

export const blog = {
  client: 'Unseal',
  title: 'Get a hodl of your health',
  description:
    'Unseal is the first NFT platform where users can mint and trade NFTs of their own personal health records, allowing them to take control of their data.',
  summary: [
    'Unseal is the first NFT platform where users can mint and trade NFTs of their own personal health records, allowing them to take control of their data.',
    'We built out the blockchain infrastructure that supports Unseal. Unfortunately, we took a massive loss on this project when Unseal’s cryptocurrency, PlaceboCoin, went to zero.',
  ],
  logo,
  image: { src: imageHero },
  date: '2022-10',
  service: 'Blockchain development',
  testimonial: {
    author: { name: '<PERSON>', role: 'Head of Engineering at Unseal' },
    content:
      'Studio did an amazing job building out our core blockchain infrastructure and I’m sure once PlaceboCoin rallies they’ll be able to finish the project.',
  },
}

export const metadata = {
  title: `${blog.client} Blog`,
  description: blog.description,
}

## Overview

Annoyed that his wife’s gynaecologist would not disclose the results of her pap smear, Unseal’s founder <PERSON> came up with the idea of using the block chain to store individual health records.

Unseal approached us early in their development, having just raised funds through an ICO of their cryptocurrency PlaceboCoin. Having never worked on a web3 product we decided to farm the project out to an agency in Kyiv and skim profits off the top. Despite frequent complaints about missile strikes and power outages, the Ukrainians delivered the brief ahead of schedule.

After reaching a high of $12k, PlaceboCoin went to zero in a matter of hours. Because we took payment in PlaceboCoin but our subcontractors insisted on being paid in USD we have taken a huge financial loss on this project.

## What we did

<TagList>
  <TagListItem>Blockchain development</TagListItem>
  <TagListItem>Backend (Solidity)</TagListItem>
  <TagListItem>Smart contracts</TagListItem>
</TagList>

<Blockquote
  author={{ name: 'Emily Selman', role: 'Head of Engineering at Unseal' }}
  image={{ src: imageEmilySelman }}
>
  Studio did an amazing job building out our core blockchain infrastructure and
  I’m sure once PlaceboCoin rallies they’ll be able to finish the project.
</Blockquote>

<StatList>
  <StatListItem value="34%" label="Fewer transactions" />
  <StatListItem value="10%" label="Slower transactions" />
  <StatListItem value="1000ms" label="Transaction latency" />
  <StatListItem value="3" label="Active nodes" />
</StatList>
